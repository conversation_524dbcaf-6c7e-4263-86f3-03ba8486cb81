package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.group.QueryLogic;
import cn.hanyi.ctm.dto.group.EventGroupQueryItemsDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryItemsDtoConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "event_result_group_query")
@AllArgsConstructor
@NoArgsConstructor
@DtoClass
public class EventGroupQuery extends EnterpriseOwnerEntity  {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "group_id")
    @DtoProperty(ignore = true)
    private EventGroup group;

    @Schema(description = "顺序")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private int sequence = 0;

    @Schema(description = "查询逻辑")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private QueryLogic logic = QueryLogic.and;

    @Schema(description = "操作类型")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Convert(converter = EventGroupQueryItemsDtoConverter.class)
    private EventGroupQueryItemsDto property;

}
