package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.group.GroupType;
import cn.hanyi.ctm.constant.group.QueryLogic;
import cn.hanyi.ctm.constant.survey.SurveyChangeStatus;
import cn.hanyi.ctm.dto.event.SimpleEventMonitorRules;
import cn.hanyi.ctm.dto.group.EventGroupQueryItemsDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryPropertyDto;
import cn.hanyi.ctm.dto.survey.SurveyResponseCellMessageRuleDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.properites.EventResultQueryProperties;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.criteria.Predicate;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EventQueryService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private EventGroupService eventGroupService;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private EventResultQueryProperties eventResultQueryProperties;
    @Autowired
    private EventService eventService;
    private final List<Field> EVENT_ENUM_FIELDS = new ArrayList<>();

    private Map<String, Map<String, String>> ColumnFiledValue = Map.of(
            "warning_level", Map.of(
                    "NONE", "0",
                    "LOW", "1",
                    "MIDDLE", "2",
                    "HIGH", "3"
            ),
            "status", Map.of(
                    "NONE", "0",
                    "WAIT", "1",
                    "APPLYING", "2",
                    "SUCCESS", "3"
            )
    );


    /**
     * 添加预警规则
     */
    public EventDto insertRules(EventDto event) {

        if (event != null) {
            LinkedHashMap<Long, SimpleEventMonitorRules> questionIdRules = new LinkedHashMap<>();
            event.getWarnings().forEach(rId -> {
                Long ruleId = rId.getRuleId();
                if (ruleId != null && ruleId > 0 && event.getQuestions() != null && CollectionUtils.isNotEmpty(event.getQuestions())) {
                    event.getQuestions().forEach(q -> {
                        if (q.getEventMonitorRules() != null && ruleId.equals(q.getEventMonitorRules().getId())) {
                            questionIdRules.put(q.getQuestionId(),
                                    new SimpleEventMonitorRules(
                                            ruleId,
                                            q.getEventMonitorRules().getTitle(),
                                            q.getEventMonitorRules().getLevel(),
                                            q.getEventMonitorRules().getStatus())
                            );
                        }
                    });
                }
            });
            if (deletedSurvey(event.getSurveyId())) {
                event.setSurveyStatus(SurveyChangeStatus.DELETED);
            }
            event.setQuestionIdRules(questionIdRules);
        }
        return event;
    }


    public void insertChannelType(EventDto event) {
        if (event != null && event.getResponseId() != null) {
            Optional<SurveyResponse> responseOpt = surveyResponseRepository.findById(event.getResponseId());
            responseOpt.ifPresent(
                    response -> event.setChannelType(response.getCollectorMethod().name()));
        }
    }

    /**
     * 查询问卷是否被删除
     */
    public Boolean deletedSurvey(Long id) {
        try {
            String sql = String.format("select count(id) from survey where id =%s and deleted=0", id);
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            return count == null || count <= 0;
        } catch (Exception e) {
            log.error("查询问卷是否被删除失败", e);
            return false;
        }
    }

    /**
     * 显示的单个命中的预警题型
     */
    public void insertQuestion(List<EventDto> events) {
        events.forEach(event -> {
            insertRules(event);
            if (event.getQuestions() != null && CollectionUtils.isNotEmpty(event.getQuestions())) {
                Optional.ofNullable(event.getQuestionIdRules()).ifPresent(questionIdRules -> {
                    Optional<Map.Entry<Long, SimpleEventMonitorRules>> maxRule = questionIdRules.entrySet().stream().max(Comparator.comparing(o -> o.getValue().getLevel()));

                    SurveyResponseCellMessageRuleDto question =
                            maxRule.isPresent()
                                    ? maxRule.flatMap(entry -> event.getQuestions().stream().filter(q -> entry.getKey().equals(q.getQuestionId())).findFirst()).orElse(event.getQuestions().get(0))
                                    : event.getQuestions().get(0);
                    event.setQuestion(question);
                });
            }
        });
    }

    /**
     * 顾客数据
     */
    public void insertCustomer(List<EventDto> events) {
        events.forEach(event -> {
            Long customerId = event.getCustomerId();
            String externalUserId = event.getExternalUserId();
            Customer customer =
                    customerId != null
                            ? customerService.get(customerId) :
                            (
                                    externalUserId != null ? customerService.getCustomerByExternalUserId(externalUserId) : null
                            );
            if (customer != null) {
                CustomerDto customerDto = customerService.mapToDto(customer);
                customerService.afterMapToDto(List.of(customer), List.of(customerDto));
                event.setCustomer(customerDto);
                event.setCustomerName(event.getCustomerName() == null ? customerDto.getUsername() : event.getCustomerName());
            }
        });
    }

    public Page<EventDto> queryByGroupId(Long groupId, ResourceEntityQueryDto<EventDto> query) {
        if (query.getSorts().isUnsorted()) {
            query.setSorts(Sort.by(Sort.Order.desc("id")));
        }

        AtomicReference<Page<EventDto>> page = new AtomicReference<>(Page.empty());

        Optional.ofNullable(eventGroupService.get(groupId)).ifPresent(group -> {

            if (eventGroupService.findQueryByGroupId(groupId).isEmpty()) {
                return;
            }

            SqlBuilder sqlBuilder = searchSqlBuilder(
                    TenantContext.getCurrentTenant(),
                    TenantContext.getCurrentUserId(),
                    TenantContext.getCurrentDepartmentIds(),
                    group,
                    group.isWithDepartment()
            );

            Optional.ofNullable(nativeSqlHelper.queryIds(sqlBuilder)).ifPresent(ids -> {
                Page<Event> eventPage = eventService.scopeQuery(EntityScopeStrategyType.NONE, () ->
                        eventRepository.findAll(
                                (r, q, b) -> {
                                    List<Predicate> predicates = new ArrayList<>();
                                    predicates.add(r.get("id").in(ids));
                                    query.getQueryCriteriaList().forEach(c -> predicates.add(c.toPredicate(r, b)));
                                    return b.and(predicates.toArray(new Predicate[0]));
                                }, PageRequest.of(query.getPage() - 1, query.getLimit(), query.getSorts()))
                );

                Page<EventDto> eventDtoPage = eventPage.map(eventService::mapToDto);
                List<Event> eventList = eventPage.getContent();
                List<EventDto> dtoList = eventDtoPage.getContent();
                eventService.afterMapToDto(eventList, dtoList);

                page.set(eventDtoPage);
            });
        });

        return page.get();

    }

    public CountDto countByGroupId(Long groupId, ResourceEntityQueryDto<EventDto> query) {
        return countByGroupId(
                TenantContext.requireCurrentTenant(),
                TenantContext.requireCurrentUserId(),
                TenantContext.getCurrentDepartmentIds(),
                groupId,
                query,
                EventGroup::isWithDepartment);
    }

    public CountDto countByGroupId(Long orgId, Long userId, List<Long> departmentIds, Long groupId,Function<EventGroup, Boolean> withDepartment ) {
        ResourceEntityQueryDto<EventDto> query = new ResourceEntityQueryDto<>();
        query.setBy("status");
        query.addCriteria(new ResourceQueryCriteria("warningLevel", EventWarningType.NONE, QueryOperator.NOT_EQUAL));
        return countByGroupId(
                orgId,
                userId,
                departmentIds,
                groupId,
                query,
                withDepartment);
    }

    public CountDto countByGroupId(Long orgId, Long userId, List<Long> departmentIds, Long groupId, ResourceEntityQueryDto<EventDto> query, Function<EventGroup, Boolean> withDepartment) {
        CountDto countDto = new CountDto(0);
        Optional.ofNullable(eventGroupService.get(groupId)).ifPresent(group -> {

            if (eventGroupService.findQueryByGroupId(groupId).isEmpty()) {
                return;
            }

            SqlBuilder sqlBuilder = searchSqlBuilder(
                    orgId,
                    userId,
                    departmentIds,
                    group,
                    withDepartment.apply(group)
            );

            List<Long> ids = nativeSqlHelper.queryIds(sqlBuilder);
            if (!ids.isEmpty()) {
                eventService.scopeQuery(EntityScopeStrategyType.NONE, () -> {
                    countDto.setTotal(
                            eventRepository.count((root, q, cb) -> {
                                List<Predicate> predicates = new ArrayList<>();
                                predicates.add(root.get("id").in(ids));
                                Optional.ofNullable(query.getBy()).ifPresent(by -> {
                                    q.groupBy(root.get(by));
                                });
                                query.getQueryCriteriaList().forEach(c -> predicates.add(c.toPredicate(root, cb)));
                                return cb.and(predicates.toArray(new Predicate[0]));
                            })
                    );

                    Optional.ofNullable(query.getBy()).ifPresent(by -> {
                        GenericSpecification<Event> genericSpecification = new GenericSpecification<>(query);
                        genericSpecification.add(new ResourceQueryCriteria("id", ids, QueryOperator.IN));
                        countDto.setItems(eventRepository.countBy(by, genericSpecification));
                    });
                    return 0;
                });
            }

        });
        return countDto;

    }


    public SqlBuilder searchSqlBuilder(Long orgId, Long userId, List<Long> departmentIds, EventGroup group, boolean withDepartment) {

        String countSql = "select %s count(*) count from event_result er";
        String bySql = "";

        SqlBuilder sqlBuilder = SqlBuilder.select("select distinct er.id,er.org_id orgId from event_result er");

        sqlBuilder.countSelect(String.format(countSql, bySql));

        // Build nested logical structure for WHERE clause
        List<String> groupConditions = new ArrayList<>();

        for (EventGroupQueryPropertyDto queries : eventGroupService.queryGroupsByGroupId(group.getId())) {
            List<String> itemConditions = new ArrayList<>();

            for (EventGroupQueryItemsDto itemsDto : queries.getItems()) {
                EventResultQueryProperties.Query query = eventResultQueryProperties.getQuery().stream().filter(q -> q.getTemplateId().equals(itemsDto.getTemplateId())).findFirst().orElse(null);
                String name = Objects.requireNonNull(query).getPropertyName();
                String column = Objects.requireNonNull(query).getPropertyColumn();
                String on = query.getPropertyOn();
                String table = query.getPropertySource();
                String tableAlias = table == null ? "er" : itemsDto.tableAlias();

                if (column != null) {

                    String finalColumn = column;
                    String value = ColumnFiledValue.containsKey(column)
                            ? Arrays.asList(itemsDto.getQueryValue().split(",")).stream().map(v -> ColumnFiledValue.get(finalColumn).get(v)).collect(Collectors.joining(","))
                            : itemsDto.getQueryValue();
                    String join = EventResultQueryProperties.ColumnType.function.equals(query.getColumnType()) ? "" : tableAlias + ".";

                    // 事件行动时间需要单独处理
                    if (query.getTemplateId().equals(9)) {
                        sqlBuilder.join(String.format("left join %1$s %2$s on er.%3$s=%2$s.%4$s", table, tableAlias, name, on == null ? column : on)).ifTrue(true);
                        column = tableAlias + "." + column;
                    }

                    String where = itemsDto.getQueryValueType().query(itemsDto.getQueryType(), column, value);
                    sqlBuilder.join(String.format("left join %1$s %2$s on er.%3$s=%2$s.%4$s", table, tableAlias, name, on == null ? column : on)).ifTrue(table != null && StringUtils.isNotEmpty(join));
                    itemConditions.add(join + where);
                }
            }

            // Combine items within this group using the group's logic
            if (!itemConditions.isEmpty()) {
                QueryLogic groupLogic = queries.getLogic() != null ? queries.getLogic() : QueryLogic.and;
                String groupCondition = itemConditions.stream()
                        .collect(Collectors.joining(String.format(" %s ", groupLogic), "(", ")"));
                groupConditions.add(groupCondition);
            }
        }

        // Combine groups using the top-level logic
        if (!groupConditions.isEmpty()) {
            QueryLogic topLevelLogic = GroupType.MANUAL.equals(group.getType())
                    ? QueryLogic.or
                    : (group.getLogic() != null ? group.getLogic() : QueryLogic.and);

            String finalWhereClause = groupConditions.stream()
                    .collect(Collectors.joining(String.format(" %s ", topLevelLogic), "(", ")"));

            sqlBuilder.where(() -> finalWhereClause).ifTrue(true);
        }

        sqlBuilder.where("er.department_id in (" + departmentIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")").ifTrue(withDepartment);
        sqlBuilder.where("er.org_id=" + orgId).ifTrue(true);

        log.info("sqlBuilder:{}", sqlBuilder.buildSelectSql());
        return sqlBuilder;
    }


}
